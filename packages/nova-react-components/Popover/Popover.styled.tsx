import { styled } from '@pigment-css/react';
import { Modal } from '../Modal';
import { Paper } from '../Paper';

export const PopoverRoot = styled(Modal, {
  name: 'NovaPopover',
  slot: 'Root',
})({});

export const PopoverPaper = styled(Paper, {
  name: 'NovaPopover',
  slot: 'Paper',
})({
  position: 'absolute',
  overflowY: 'auto',
  overflowX: 'visible', // Changed from 'hidden' to 'visible' to allow arrow to show
  // So we see the popover when it's empty.
  // It's most likely on issue on userland.
  minWidth: 16,
  minHeight: 16,
  maxWidth: 'calc(100% - 32px)',
  maxHeight: 'calc(100% - 32px)',
  // We disable the focus ring for mouse, touch and keyboard users.
  outline: 0,
  padding: '16px',
  borderRadius: '8px',
  margin: '8px',
});

export const PopoverArrow = styled('span', {
  name: 'NovaPopover',
  slot: 'Arrow',
})(({ theme }) => ({
  overflow: 'visible',
  position: 'absolute',
  width: '20px',
  height: '20px',
  boxSizing: 'border-box',
  zIndex: 10,
  pointerEvents: 'none',
  // Default position will be overridden by inline styles
  '&::before': {
    content: '""',
    margin: 'auto',
    display: 'block',
    width: '100%',
    height: '100%',
    backgroundColor: theme.vars.palette.surfaceContainer,
    transform: 'rotate(45deg)',
    border: `1px solid ${theme.vars.palette.outline}`,
    borderRadius: '2px',
  },
}));
