import { styled } from '@pigment-css/react';
import { Modal } from '../Modal';
import { Paper } from '../Paper';

export const PopoverRoot = styled(Modal, {
  name: 'NovaPopover',
  slot: 'Root',
})({});

export const PopoverPaper = styled(Paper, {
  name: 'NovaPopover',
  slot: 'Paper',
})({
  position: 'absolute',
  overflowY: 'auto',
  overflowX: 'visible', // Allow arrow to extend outside bounds
  // So we see the popover when it's empty.
  // It's most likely on issue on userland.
  minWidth: 16,
  minHeight: 16,
  maxWidth: 'calc(100% - 32px)',
  maxHeight: 'calc(100% - 32px)',
  // We disable the focus ring for mouse, touch and keyboard users.
  outline: 0,
  padding: '16px',
  borderRadius: '8px',
  margin: '8px',
});

export const PopoverArrow = styled('span', {
  name: 'NovaPopover',
  slot: 'Arrow',
})(({ theme }) => ({
  overflow: 'visible',
  position: 'absolute',
  width: '20px',
  height: '20px',
  boxSizing: 'border-box',
  zIndex: 1001, // Higher than paper to ensure visibility
  pointerEvents: 'none',
  // Default position will be overridden by inline styles
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '12px',
    height: '12px',
    backgroundColor: theme.vars.palette.surfaceContainer,
    transform: 'translate(-50%, -50%) rotate(45deg)',
    border: `1px solid ${theme.vars.palette.outline}`,
    borderRadius: '2px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
  },
}));
