'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { PopoverProps } from './Popover.types';
import { getPopoverUtilityClass } from './Popover.classes';
import { PopoverRoot, PopoverPaper, PopoverArrow } from './Popover.styled';
import ownerDocument from '@mui/utils/ownerDocument';
import ownerWindow from '@mui/utils/ownerWindow';
import debounce from '@mui/utils/debounce';
import { Grow } from '../Grow';
import { isHostComponent } from '../utils';
import useSlotProps from '@mui/utils/useSlotProps';

export function getOffsetTop(rect: DOMRect, vertical: number | 'center' | 'bottom' | 'top'): number {
  let offset = 0;

  if (typeof vertical === 'number') {
    offset = vertical;
  } else if (vertical === 'center') {
    offset = rect.height / 2;
  } else if (vertical === 'bottom') {
    offset = rect.height;
  }

  return offset;
}

export function getOffsetLeft(rect: DOMRect, horizontal: number | 'center' | 'right' | 'left'): number {
  let offset = 0;

  if (typeof horizontal === 'number') {
    offset = horizontal;
  } else if (horizontal === 'center') {
    offset = rect.width / 2;
  } else if (horizontal === 'right') {
    offset = rect.width;
  }

  return offset;
}

function getTransformOriginValue(transformOrigin: any) {
  return [transformOrigin.horizontal, transformOrigin.vertical]
    .map((n: any) => (typeof n === 'number' ? `${n}px` : n))
    .join(' ');
}

function resolveAnchorEl(anchorEl: any) {
  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;
}

const useUtilityClasses = (ownerState: any) => {
  const { classes } = ownerState;

  const slots = {
    root: ['root'],
    paper: ['paper'],
    arrow: ['arrow'],
  };

  return composeClasses(slots, getPopoverUtilityClass, classes);
};

// eslint-disable-next-line react/display-name
export const Popover = React.forwardRef<HTMLDivElement, PopoverProps>((props, ref) => {
  const {
    action,
    anchorEl,
    anchorOrigin = {
      vertical: 'top',
      horizontal: 'left',
    },
    anchorPosition,
    anchorReference = 'anchorEl',
    children,
    className,
    container: containerProp,
    elevation = 8,
    marginThreshold = 16,
    open,
    showArrow = false,
    slots = {} as PopoverProps['slots'],
    slotProps = {} as PopoverProps['slotProps'],
    transformOrigin = {
      vertical: 'top',
      horizontal: 'left',
    },
    transitionDuration: transitionDurationProp = 'auto',
    disableScrollLock = false,
    ...other
  } = props;

  const paperRef = React.useRef<HTMLElement>();

  const ownerState = {
    ...props,
    anchorOrigin,
    anchorReference,
    elevation,
    marginThreshold,
    showArrow,
    transformOrigin,
    transitionDuration: transitionDurationProp,
  };

  const classes = useUtilityClasses(ownerState);

  // Returns the top/left offset of the position
  // to attach to on the anchor element (or body if none is provided)
  const getAnchorOffset = React.useCallback(() => {
    if (anchorReference === 'anchorPosition') {
      if (process.env.NODE_ENV !== 'production') {
        if (!anchorPosition) {
          console.error(
            'Nova: You need to provide a `anchorPosition` prop when using ' +
              '<Popover anchorReference="anchorPosition" />.',
          );
        }
      }
      return anchorPosition;
    }

    const resolvedAnchorEl = resolveAnchorEl(anchorEl);

    // If an anchor element wasn't provided, just use the parent body element of this Popover
    const anchorElement =
      resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;
    const anchorRect = anchorElement.getBoundingClientRect();

    if (process.env.NODE_ENV !== 'production') {
      const box = anchorElement.getBoundingClientRect();

      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {
        console.warn(
          [
            'Nova: The `anchorEl` prop provided to the component is invalid.',
            'The anchor element should be part of the document layout.',
            "Make sure the element is present in the document or that it's not display none.",
          ].join('\n'),
        );
      }
    }

    return {
      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),
      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal),
    };
  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);

  // Returns the base transform origin using the element
  const getTransformOrigin = React.useCallback(
    (elemRect: any) => {
      return {
        vertical: getOffsetTop(elemRect, transformOrigin.vertical),
        horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal),
      };
    },
    [transformOrigin.horizontal, transformOrigin.vertical],
  );

  const getPositioningStyle = React.useCallback(
    (element: any) => {
      const elemRect = {
        width: element.offsetWidth,
        height: element.offsetHeight,
      };

      // Get the transform origin point on the element itself
      const elemTransformOrigin = getTransformOrigin(elemRect);

      if (anchorReference === 'none') {
        return {
          top: null,
          left: null,
          transformOrigin: getTransformOriginValue(elemTransformOrigin),
        };
      }

      // Get the offset of the anchoring element
      const anchorOffset = getAnchorOffset();

      // Calculate element positioning
      let top = anchorOffset?.top - elemTransformOrigin.vertical;
      let left = anchorOffset?.left - elemTransformOrigin.horizontal;
      const bottom = top + elemRect.height;
      const right = left + elemRect.width;

      // Use the parent window of the anchorEl if provided
      const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));

      // Window thresholds taking required margin into account
      const heightThreshold = containerWindow.innerHeight - (marginThreshold || 0);
      const widthThreshold = containerWindow.innerWidth - (marginThreshold || 0);

      // Check if the vertical axis needs shifting
      if (marginThreshold !== null && top < marginThreshold) {
        const diff = top - marginThreshold;

        top -= diff;

        elemTransformOrigin.vertical += diff;
      } else if (marginThreshold !== null && bottom > heightThreshold) {
        const diff = bottom - heightThreshold;

        top -= diff;

        elemTransformOrigin.vertical += diff;
      }

      if (process.env.NODE_ENV !== 'production') {
        if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {
          console.error(
            [
              'Nova: The popover component is too tall.',
              `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`,
              'Please consider adding a `max-height` to improve the user-experience.',
            ].join('\n'),
          );
        }
      }

      // Check if the horizontal axis needs shifting
      if (marginThreshold !== null && left < marginThreshold) {
        const diff = left - marginThreshold;
        left -= diff;
        elemTransformOrigin.horizontal += diff;
      } else if (right > widthThreshold) {
        const diff = right - widthThreshold;
        left -= diff;
        elemTransformOrigin.horizontal += diff;
      }

      return {
        top: `${Math.round(top)}px`,
        left: `${Math.round(left)}px`,
        transformOrigin: getTransformOriginValue(elemTransformOrigin),
      };
    },
    [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold],
  );

  const [isPositioned, setIsPositioned] = React.useState(open);

  const setPositioningStyles = React.useCallback(() => {
    const element = paperRef.current;

    if (!element) {
      return;
    }

    const positioning = getPositioningStyle(element);

    if (positioning.top !== null) {
      (element as HTMLElement).style.setProperty('top', positioning.top);
    }
    if (positioning.left !== null) {
      (element as HTMLElement).style.left = positioning.left;
    }
    (element as HTMLElement).style.transformOrigin = positioning.transformOrigin;
    setIsPositioned(true);
  }, [getPositioningStyle]);

  React.useEffect(() => {
    if (disableScrollLock) {
      window.addEventListener('scroll', setPositioningStyles);
    }
    return () => window.removeEventListener('scroll', setPositioningStyles);
  }, [anchorEl, disableScrollLock, setPositioningStyles]);

  const handleEntering = () => {
    setPositioningStyles();
  };

  const handleExited = () => {
    setIsPositioned(false);
  };

  React.useEffect(() => {
    if (open) {
      setPositioningStyles();
    }
  });

  React.useImperativeHandle(
    action,
    () =>
      open
        ? {
            updatePosition: () => {
              setPositioningStyles();
            },
          }
        : ({} as any),
    [open, setPositioningStyles],
  );

  React.useEffect(() => {
    if (!open) {
      return undefined;
    }

    const handleResize = debounce(() => {
      setPositioningStyles();
    });

    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));
    containerWindow.addEventListener('resize', handleResize);
    return () => {
      handleResize.clear();
      containerWindow.removeEventListener('resize', handleResize);
    };
  }, [anchorEl, open, setPositioningStyles]);

  let transitionDuration = transitionDurationProp;

  const TransitionSlot = slots?.transition ?? Grow;
  const transitionSlotProps = useSlotProps({
    elementType: Grow,
    externalSlotProps: slotProps?.transition,
    additionalProps: {
      appear: true,
      in: open,
      onEntering: (element: HTMLElement, isAppearing: boolean) => {
        if (typeof slotProps?.transition === 'function') {
          slotProps?.transition(ownerState)?.onEntering?.(element, isAppearing);
        } else {
          slotProps?.transition?.onEntering?.(element, isAppearing);
        }
        handleEntering();
      },
      onExited: (element: HTMLElement) => {
        if (typeof slotProps?.transition === 'function') {
          slotProps?.transition(ownerState)?.onExited?.(element);
        } else {
          slotProps?.transition?.onExited?.(element);
        }
        handleExited();
      },
    },
    ownerState,
  });

  if (transitionDurationProp === 'auto' && !(TransitionSlot as any).muiSupportAuto) {
    transitionDuration = 'auto' as any;
  }

  // If the container prop is provided, use that
  // If the anchorEl prop is provided, use its parent body element as the container
  // If neither are provided let the Modal take care of choosing the container
  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);

  const RootSlot = slots?.root ?? PopoverRoot;
  const rootProps = useSlotProps({
    elementType: PopoverRoot,
    externalSlotProps: slotProps?.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      slots: { backdrop: slots?.backdrop },
      slotProps: {
        backdrop: {
          ...(typeof slotProps?.backdrop === 'function' ? slotProps?.backdrop(ownerState) : slotProps?.backdrop || {}),
          invisible: true,
        },
      },
      container,
      open,
    },
    ownerState,
    className: clsx(classes.root, className),
  });

  const PaperSlot = slots?.paper ?? PopoverPaper;
  const paperProps = useSlotProps({
    elementType: PopoverPaper,
    externalSlotProps: slotProps?.paper,
    additionalProps: {
      ref: paperRef,
      elevation,
      style: isPositioned ? undefined : { opacity: 0 },
    },
    ownerState,
    className: classes.paper,
  });

  // Calculate arrow position based on anchor and transform origins
  const getArrowStyle = React.useCallback(() => {
    let arrowStyle: React.CSSProperties = {};
    const arrowSize = 20;
    const offset = arrowSize / 2;
    const paperMargin = 8; // Match the margin from PopoverPaper

    // If popover is positioned below the anchor (anchor bottom, transform top)
    // Arrow should point UP to the anchor
    if (anchorOrigin.vertical === 'bottom' && transformOrigin.vertical === 'top') {
      arrowStyle = {
        top: `-${offset + paperMargin}px`,
        left: '50%',
        transform: 'translateX(-50%)',
      };
    }
    // If popover is positioned above the anchor (anchor top, transform bottom)
    // Arrow should point DOWN to the anchor
    else if (anchorOrigin.vertical === 'top' && transformOrigin.vertical === 'bottom') {
      arrowStyle = {
        bottom: `-${offset + paperMargin}px`,
        left: '50%',
        transform: 'translateX(-50%) rotate(180deg)',
      };
    }
    // If popover is positioned to the right of the anchor (anchor right, transform left)
    // Arrow should point LEFT to the anchor
    else if (anchorOrigin.horizontal === 'right' && transformOrigin.horizontal === 'left') {
      arrowStyle = {
        left: `-${offset + paperMargin}px`,
        top: '50%',
        transform: 'translateY(-50%) rotate(-90deg)',
      };
    }
    // If popover is positioned to the left of the anchor (anchor left, transform right)
    // Arrow should point RIGHT to the anchor
    else if (anchorOrigin.horizontal === 'left' && transformOrigin.horizontal === 'right') {
      arrowStyle = {
        right: `-${offset + paperMargin}px`,
        top: '50%',
        transform: 'translateY(-50%) rotate(90deg)',
      };
    }
    // Default position (bottom arrow pointing up)
    else {
      arrowStyle = {
        top: `-${offset + paperMargin}px`,
        left: '50%',
        transform: 'translateX(-50%)',
      };
    }

    return arrowStyle;
  }, [anchorOrigin, transformOrigin]);

  const ArrowSlot = slots?.arrow ?? PopoverArrow;
  const arrowProps = useSlotProps({
    elementType: PopoverArrow,
    externalSlotProps: slotProps?.arrow,
    additionalProps: {
      style: getArrowStyle(),
    },
    ownerState,
    className: classes.arrow,
  });

  return (
    <RootSlot
      {...rootProps}
      {...(!isHostComponent(RootSlot) && {
        disableScrollLock,
      })}
    >
      <TransitionSlot {...transitionSlotProps} timeout={transitionDuration}>
        <PaperSlot {...paperProps}>
          {children}
          {showArrow && <ArrowSlot {...arrowProps} />}
        </PaperSlot>
      </TransitionSlot>
    </RootSlot>
  );
});
