import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { Popover } from './Popover';
import { Button } from '../Button';
import { Box } from '../Box';

describe('Popover', () => {
  it('renders without crashing', () => {
    render(
      <Popover open={false} anchorEl={null} onClose={() => {}}>
        <div>Test content</div>
      </Popover>,
    );
  });

  it('renders content when open', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover open={true} anchorEl={anchorEl} onClose={() => {}}>
        <div>Test content</div>
      </Popover>,
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('renders arrow when showArrow is true', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover open={true} anchorEl={anchorEl} onClose={() => {}} showArrow={true} data-testid="popover">
        <div>Test content</div>
      </Popover>,
    );

    // Check if arrow element is present
    const popover = screen.getByTestId('popover');
    const arrow = popover.querySelector('[class*="arrow"]');
    expect(arrow).toBeInTheDocument();
  });

  it('does not render arrow when showArrow is false', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover open={true} anchorEl={anchorEl} onClose={() => {}} showArrow={false} data-testid="popover">
        <div>Test content</div>
      </Popover>,
    );

    // Check if arrow element is not present
    const popover = screen.getByTestId('popover');
    const arrow = popover.querySelector('[class*="arrow"]');
    expect(arrow).not.toBeInTheDocument();
  });

  it('positions arrow correctly for bottom placement', () => {
    const anchorEl = document.createElement('div');
    render(
      <Popover
        open={true}
        anchorEl={anchorEl}
        onClose={() => {}}
        showArrow={true}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        data-testid="popover"
      >
        <div>Test content</div>
      </Popover>,
    );

    // Check if arrow element has correct positioning styles
    const popover = screen.getByTestId('popover');
    const arrow = popover.querySelector('[class*="arrow"]');
    expect(arrow).toBeInTheDocument();
    expect(arrow).toHaveStyle({
      position: 'absolute',
    });
  });

  it('calls onClose when backdrop is clicked', async () => {
    const user = userEvent.setup();
    const onClose = vi.fn();
    const anchorEl = document.createElement('div');

    render(
      <Popover
        open={true}
        anchorEl={anchorEl}
        onClose={onClose}
        slots={{
          backdrop: 'div',
        }}
      >
        <div>Test content</div>
      </Popover>,
    );

    // Click on backdrop (outside the popover content)
    const backdrop = document.querySelector('[class*="backdrop"]');
    if (backdrop) {
      await user.click(backdrop);
      expect(onClose).toHaveBeenCalled();
    }
  });
});
