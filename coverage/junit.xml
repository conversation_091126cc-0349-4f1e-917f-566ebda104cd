<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="6" failures="1" errors="0" time="0.080315625">
    <testsuite name="Popover/Popover.test.tsx" timestamp="2025-07-07T07:03:47.260Z" hostname="X-CN-7VYQNV.local" tests="6" failures="1" errors="0" skipped="0" time="0.080315625">
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; renders without crashing" time="0.00753525">
        </testcase>
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; renders content when open" time="0.0360045">
        </testcase>
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; renders arrow when showArrow is true" time="0.005473708">
        </testcase>
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; does not render arrow when showArrow is false" time="0.003812">
        </testcase>
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; positions arrow correctly for bottom placement" time="0.008865667">
            <failure message="[2mexpect([22m[31melement[39m[2m).toHaveStyle()[22m

[32m- Expected[39m

[32m- position: absolute;[39m" type="Error">
Error: expect(element).toHaveStyle()

- Expected

- position: absolute;
 ❯ Popover/Popover.test.tsx:94:15
            </failure>
        </testcase>
        <testcase classname="Popover/Popover.test.tsx" name="Popover &gt; calls onClose when backdrop is clicked" time="0.018148375">
            <system-err>
Warning: Received `true` for a non-boolean attribute `invisible`.

If you want to write it to the DOM, pass a string instead: invisible=&quot;true&quot; or invisible={value.toString()}.
    at div
    at div
    at StyledComponent2 (file:///Users/<USER>/projects/Nova/node_modules/.pnpm/@pigment-css+react@0.0.30_@types+react@18.3.23_react@18.3.1_typescript@5.6.3/node_modules/@pigment-css/react/build/chunk-AFZBAV6Q.mjs:51:29)
    at Portal2 (/Users/<USER>/projects/Nova/packages/nova-react-components/Portal/Portal.tsx:12:5)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Modal/Modal.tsx:28:14
    at StyledComponent2 (file:///Users/<USER>/projects/Nova/node_modules/.pnpm/@pigment-css+react@0.0.30_@types+react@18.3.23_react@18.3.1_typescript@5.6.3/node_modules/@pigment-css/react/build/chunk-AFZBAV6Q.mjs:51:29)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Popover/Popover.tsx:58:5

            </system-err>
        </testcase>
    </testsuite>
</testsuites>
